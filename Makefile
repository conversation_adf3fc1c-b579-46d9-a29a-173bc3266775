# Project Obsoletion - Neural Network Bootloader Makefile
# Author: Augment Agent

# Assembler and linker
ASM = nasm
LD = ld
DD = dd

# Flags
ASMFLAGS = -f elf32
LDFLAGS = -m elf_i386 -T linker.ld

# Source files
BOOTLOADER_SRC = bootloader.asm
NEURAL_CORE_SRC = neural_core.asm
SYSCALL_HOOK_SRC = syscall_hook.asm
RAG_SYSTEM_SRC = rag_system.asm

# Object files
BOOTLOADER_OBJ = bootloader.o
NEURAL_CORE_OBJ = neural_core.o
SYSCALL_HOOK_OBJ = syscall_hook.o
RAG_SYSTEM_OBJ = rag_system.o

# Output files
BOOTLOADER_BIN = bootloader.bin
NEURAL_KERNEL = neural_kernel.bin
DISK_IMAGE = obsoletion.img

# Default target
all: $(DISK_IMAGE)

# Build bootloader binary
$(BOOTLOADER_BIN): $(BOOTLOADER_SRC)
	$(ASM) -f bin -o $@ $<

# Build object files
$(BOOTLOADER_OBJ): $(BOOTLOADER_SRC)
	$(ASM) $(ASMFLAGS) -o $@ $<

$(NEURAL_CORE_OBJ): $(NEURAL_CORE_SRC)
	$(ASM) $(ASMFLAGS) -o $@ $<

$(SYSCALL_HOOK_OBJ): $(SYSCALL_HOOK_SRC)
	$(ASM) $(ASMFLAGS) -o $@ $<

$(RAG_SYSTEM_OBJ): $(RAG_SYSTEM_SRC)
	$(ASM) $(ASMFLAGS) -o $@ $<

# Link neural kernel
$(NEURAL_KERNEL): $(NEURAL_CORE_OBJ) $(SYSCALL_HOOK_OBJ) $(RAG_SYSTEM_OBJ) linker.ld
	$(LD) $(LDFLAGS) -o $@ $(NEURAL_CORE_OBJ) $(SYSCALL_HOOK_OBJ) $(RAG_SYSTEM_OBJ)

# Create disk image
$(DISK_IMAGE): $(BOOTLOADER_BIN) $(NEURAL_KERNEL)
	# Create 1.44MB floppy disk image
	$(DD) if=/dev/zero of=$@ bs=1024 count=1440
	
	# Write bootloader to first sector
	$(DD) if=$(BOOTLOADER_BIN) of=$@ bs=512 count=1 conv=notrunc
	
	# Write neural kernel starting at sector 2
	$(DD) if=$(NEURAL_KERNEL) of=$@ bs=512 seek=1 conv=notrunc

# Test in QEMU
test: $(DISK_IMAGE)
	qemu-system-i386 -fda $(DISK_IMAGE) -boot a

# Test in QEMU with debugging
debug: $(DISK_IMAGE)
	qemu-system-i386 -fda $(DISK_IMAGE) -boot a -s -S

# Create USB bootable image
usb: $(DISK_IMAGE)
	@echo "WARNING: This will overwrite the USB device!"
	@echo "Make sure to specify the correct device with USB_DEVICE=/dev/sdX"
	@if [ -z "$(USB_DEVICE)" ]; then \
		echo "Error: USB_DEVICE not specified"; \
		echo "Usage: make usb USB_DEVICE=/dev/sdX"; \
		exit 1; \
	fi
	sudo $(DD) if=$(DISK_IMAGE) of=$(USB_DEVICE) bs=1M

# Install to hard drive MBR (DANGEROUS!)
install-mbr: $(BOOTLOADER_BIN)
	@echo "WARNING: This will overwrite your Master Boot Record!"
	@echo "This is EXTREMELY DANGEROUS and may make your system unbootable!"
	@echo "Only proceed if you know what you're doing and have backups!"
	@echo "Press Ctrl+C to cancel, or Enter to continue..."
	@read dummy
	@if [ -z "$(DRIVE)" ]; then \
		echo "Error: DRIVE not specified"; \
		echo "Usage: make install-mbr DRIVE=/dev/sdX"; \
		exit 1; \
	fi
	sudo $(DD) if=$(BOOTLOADER_BIN) of=$(DRIVE) bs=512 count=1

# Clean build files
clean:
	rm -f *.o *.bin *.img

# Create development environment
dev-setup:
	@echo "Setting up development environment..."
	@echo "Installing required packages..."
	sudo apt-get update
	sudo apt-get install -y nasm qemu-system-x86 build-essential
	@echo "Development environment ready!"

# Analyze binary size
analyze: $(BOOTLOADER_BIN) $(NEURAL_KERNEL)
	@echo "=== Binary Size Analysis ==="
	@echo "Bootloader size: $$(stat -c%s $(BOOTLOADER_BIN)) bytes"
	@echo "Neural kernel size: $$(stat -c%s $(NEURAL_KERNEL)) bytes"
	@echo "Total size: $$(($(stat -c%s $(BOOTLOADER_BIN)) + $(stat -c%s $(NEURAL_KERNEL)))) bytes"
	@echo "Bootloader must be <= 512 bytes"
	@if [ $$(stat -c%s $(BOOTLOADER_BIN)) -gt 512 ]; then \
		echo "ERROR: Bootloader exceeds 512 bytes!"; \
		exit 1; \
	fi

# Disassemble bootloader for debugging
disasm: $(BOOTLOADER_BIN)
	objdump -D -b binary -m i386 -M intel $(BOOTLOADER_BIN)

# Create documentation
docs:
	@echo "=== Project Obsoletion Documentation ==="
	@echo ""
	@echo "OVERVIEW:"
	@echo "Project Obsoletion is a neural network implemented entirely in assembly"
	@echo "that operates at the firmware level before any operating system boots."
	@echo ""
	@echo "COMPONENTS:"
	@echo "- bootloader.asm: Main bootloader with neural network initialization"
	@echo "- neural_core.asm: Neural network forward/backward pass implementation"
	@echo "- syscall_hook.asm: System call interception and analysis"
	@echo "- rag_system.asm: Retrieval-Augmented Generation with disk access"
	@echo ""
	@echo "NEURAL NETWORK ARCHITECTURE:"
	@echo "- Input Layer: 512 neurons"
	@echo "- Hidden Layer 1: 256 neurons (ReLU activation)"
	@echo "- Hidden Layer 2: 128 neurons (ReLU activation)"
	@echo "- Hidden Layer 3: 64 neurons (ReLU activation)"
	@echo "- Output Layer: 32 neurons (Softmax activation)"
	@echo "- Total Parameters: ~10,000"
	@echo ""
	@echo "MEMORY LAYOUT:"
	@echo "- 0x7C00-0x7DFF: Bootloader (512 bytes)"
	@echo "- 0x10000+: Neural network weights and biases"
	@echo "- 0x90000+: Protected mode stack"
	@echo "- 0xB8000: VGA text buffer"
	@echo ""
	@echo "BUILD COMMANDS:"
	@echo "- make all: Build complete system"
	@echo "- make test: Test in QEMU emulator"
	@echo "- make debug: Debug in QEMU with GDB"
	@echo "- make clean: Clean build files"
	@echo ""
	@echo "INSTALLATION (DANGEROUS):"
	@echo "- make usb USB_DEVICE=/dev/sdX: Create bootable USB"
	@echo "- make install-mbr DRIVE=/dev/sdX: Install to MBR (DANGEROUS!)"

# Performance benchmarks
benchmark: $(DISK_IMAGE)
	@echo "Running performance benchmarks..."
	@echo "This will test neural network inference speed in QEMU"
	timeout 30s qemu-system-i386 -fda $(DISK_IMAGE) -boot a -nographic || true

# Security analysis
security-check:
	@echo "=== Security Analysis ==="
	@echo "Checking for potential security issues..."
	@echo ""
	@echo "WARNINGS:"
	@echo "- This system operates at firmware level with full hardware access"
	@echo "- System call interception may be detected by security software"
	@echo "- Direct disk access bypasses OS security mechanisms"
	@echo "- Neural network decisions affect system behavior"
	@echo ""
	@echo "RECOMMENDATIONS:"
	@echo "- Only use in isolated test environments"
	@echo "- Implement proper access controls"
	@echo "- Add cryptographic signatures for integrity"
	@echo "- Include fail-safe mechanisms"

# Help target
help:
	@echo "Project Obsoletion Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build complete system"
	@echo "  test         - Test in QEMU emulator"
	@echo "  debug        - Debug in QEMU with GDB"
	@echo "  usb          - Create bootable USB (requires USB_DEVICE=/dev/sdX)"
	@echo "  install-mbr  - Install to MBR (DANGEROUS! requires DRIVE=/dev/sdX)"
	@echo "  clean        - Clean build files"
	@echo "  dev-setup    - Set up development environment"
	@echo "  analyze      - Analyze binary sizes"
	@echo "  disasm       - Disassemble bootloader"
	@echo "  docs         - Show documentation"
	@echo "  benchmark    - Run performance benchmarks"
	@echo "  security-check - Show security warnings"
	@echo "  help         - Show this help"

.PHONY: all test debug usb install-mbr clean dev-setup analyze disasm docs benchmark security-check help
