export default [
    {
        rules: {
          "no-cond-assign": ["error", "except-parens"],
          "no-empty-pattern": ["error", {"allowObjectPatternsAsParameters": false}],
          "no-empty": ["error", {"allowEmptyCatch": false}],
          "no-self-assign": ["error", {"props": true}],
          "getter-return": ["error", {"allowImplicit": false}],
          "no-fallthrough": ["error", {"allowEmptyCase": false}],
          "no-extra-boolean-cast": ["error", {"enforceForLogicalOperands": false}],
          "no-unsafe-optional-chaining": ["error", {"disallowArithmeticOperators": false}],
          "no-inner-declarations": ["error", "functions"],
          "no-unsafe-negation": ["error", {"enforceForOrderingRelations": false}],
          "no-redeclare": ["error", {"builtinGlobals": true}],
          "no-irregular-whitespace": ["error", {"skipStrings": true, "skipJSXText": false, "skipRegExps": false, "skipComments": false, "skipTemplates": false}],
          "no-undef": ["error", {"typeof": false}],
          "no-constant-condition": ["error", {"checkLoops": true}],
          "no-extra-semi": ["error"],
          "no-dupe-else-if": ["error"],
          "no-mixed-spaces-and-tabs": ["error"],
          "no-shadow-restricted-names": ["error"],
          "no-prototype-builtins": ["error"],
          "no-obj-calls": ["error"],
          "require-yield": ["error"],
          "no-octal": ["error"],
          "no-control-regex": ["error"],
          "no-async-promise-executor": ["error"],
          "no-empty-character-class": ["error"],
          "no-dupe-class-members": ["error"],
          "no-delete-var": ["error"],
          "no-regex-spaces": ["error"],
          "no-misleading-character-class": ["error"],
          "no-unused-labels": ["error"],
          "no-class-assign": ["error"],
          "no-import-assign": ["error"],
          "no-dupe-keys": ["error"],
          "no-const-assign": ["error"],
          "no-nonoctal-decimal-escape": ["error"],
          "for-direction": ["error"],
          "no-loss-of-precision": ["error"],
          "no-invalid-regexp": ["error"],
          "no-useless-catch": ["error"],
          "no-with": ["error"],
          "no-useless-escape": ["error"],
          "no-debugger": ["error"],
          "no-unexpected-multiline": ["error"],
          "no-dupe-args": ["error"],
          "no-case-declarations": ["error"],
          "no-ex-assign": ["error"],
          "no-unused-vars": ["error"],
          "no-new-symbol": ["error"],
          "no-global-assign": ["error"],
          "no-compare-neg-zero": ["error"],
          "no-useless-backreference": ["error"],
          "no-unsafe-finally": ["error"],
          "no-setter-return": ["error"],
          "no-unreachable": ["error"],
          "no-this-before-super": ["error"],
          "no-duplicate-case": ["error"],
          "constructor-super": ["error"],
          "no-func-assign": ["error"],
          "no-sparse-arrays": ["error"],
          "valid-typeof": ["error", {"requireStringLiterals": false}],
          "use-isnan": ["error", {"enforceForSwitchCase": true, "enforceForIndexOf": false}],
        }
    }
];