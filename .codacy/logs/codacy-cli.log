2025-06-11T21:18:57+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:18:57+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=init full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 init] args=[]
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 install] args=[] command=install
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:78) Starting installation process
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation runtime=python version=3.11.11
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation version=3.7.2 runtime=dart
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation runtime=go version=1.22.3
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation runtime=java version=17.0.10
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation runtime=node version=22.2.0
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation tool=pylint version=3.3.6
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation tool=revive version=1.7.0
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation version=1.78.0 tool=semgrep
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation version=0.59.1 tool=trivy
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation tool=dartanalyzer version=3.7.2
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation version=8.57.0 tool=eslint
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation tool=lizard version=1.17.19
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation version=6.55.0 tool=pmd
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=dart version=3.7.2
2025-06-11T21:19:05+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime version=3.7.2 downloadURL=https://storage.googleapis.com/dart-archive/channels/stable/release/3.7.2/sdk/dartsdk-linux-x64-release.zip downloadPath=/home/<USER>/.cache/codacy/runtimes/dartsdk-linux-x64-release.zip runtime=dart
2025-06-11T21:19:07+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:07+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:07+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[test_build.sh] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif test_build.sh]
2025-06-11T21:19:07+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif test_build.sh
2025-06-11T21:19:23+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime runtime=dart version=3.7.2 fileName=dartsdk-linux-x64-release.zip extractDirectory=/home/<USER>/.cache/codacy/runtimes
2025-06-11T21:19:28+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions binary=/home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart path=/home/<USER>/.cache/codacy/runtimes/dart-sdk/home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart error=chmod /home/<USER>/.cache/codacy/runtimes/dart-sdk/home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart: no such file or directory
2025-06-11T21:19:28+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime version=3.7.2 error=failed to download and extract runtime dart: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart: chmod /home/<USER>/.cache/codacy/runtimes/dart-sdk/home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart: no such file or directory runtime=dart
2025-06-11T21:19:28+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=go version=1.22.3
2025-06-11T21:19:28+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime downloadURL=https://go.dev/dl/go1.22.3.linux-amd64.tar.gz downloadPath=/home/<USER>/.cache/codacy/runtimes/go1.22.3.linux-amd64.tar.gz runtime=go version=1.22.3
2025-06-11T21:19:36+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime fileName=go1.22.3.linux-amd64.tar.gz extractDirectory=/home/<USER>/.cache/codacy/runtimes runtime=go version=1.22.3
2025-06-11T21:19:38+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions binary=/home/<USER>/.cache/codacy/runtimes/go/bin/go path=/home/<USER>/.cache/codacy/runtimes/go/home/<USER>/.cache/codacy/runtimes/go/bin/go error=chmod /home/<USER>/.cache/codacy/runtimes/go/home/<USER>/.cache/codacy/runtimes/go/bin/go: no such file or directory
2025-06-11T21:19:38+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime runtime=go version=1.22.3 error=failed to download and extract runtime go: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/go/bin/go: chmod /home/<USER>/.cache/codacy/runtimes/go/home/<USER>/.cache/codacy/runtimes/go/bin/go: no such file or directory
2025-06-11T21:19:38+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=java version=17.0.10
2025-06-11T21:19:38+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime downloadPath=/home/<USER>/.cache/codacy/runtimes/OpenJDK17U-jdk_x64_linux_hotspot_17.0.10_7.tar.gz runtime=java version=17.0.10 downloadURL=https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.10%2B7/OpenJDK17U-jdk_x64_linux_hotspot_17.0.10_7.tar.gz
2025-06-11T21:19:55+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime runtime=java version=17.0.10 fileName=OpenJDK17U-jdk_x64_linux_hotspot_17.0.10_7.tar.gz extractDirectory=/home/<USER>/.cache/codacy/runtimes
2025-06-11T21:19:58+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:58+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:58+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif PROJECT_STATUS.md] args=[PROJECT_STATUS.md]
2025-06-11T21:19:58+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif PROJECT_STATUS.md
2025-06-11T21:19:58+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions path=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java error=chmod /home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java: no such file or directory binary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java
2025-06-11T21:19:58+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime error=failed to download and extract runtime java: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java: chmod /home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java: no such file or directory runtime=java version=17.0.10
2025-06-11T21:19:58+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=node version=22.2.0
2025-06-11T21:19:58+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime runtime=node version=22.2.0 downloadURL=https://nodejs.org/dist/v22.2.0/node-v22.2.0-linux-x64.tar.gz downloadPath=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64.tar.gz
2025-06-11T21:20:03+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime version=22.2.0 fileName=node-v22.2.0-linux-x64.tar.gz extractDirectory=/home/<USER>/.cache/codacy/runtimes runtime=node
2025-06-11T21:20:04+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions binary=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node path=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node error=chmod /home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node: no such file or directory
2025-06-11T21:20:04+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime error=failed to download and extract runtime node: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node: chmod /home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node: no such file or directory runtime=node version=22.2.0
2025-06-11T21:20:04+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=python version=3.11.11
2025-06-11T21:20:04+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime downloadPath=/home/<USER>/.cache/codacy/runtimes/cpython-3.11.11+20250317-x86_64-unknown-linux-gnu-install_only.tar.gz runtime=python version=3.11.11 downloadURL=https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11+20250317-x86_64-unknown-linux-gnu-install_only.tar.gz
2025-06-11T21:20:07+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime version=3.11.11 fileName=cpython-3.11.11+20250317-x86_64-unknown-linux-gnu-install_only.tar.gz extractDirectory=/home/<USER>/.cache/codacy/runtimes runtime=python
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions error=chmod /home/<USER>/.cache/codacy/runtimes/python/home/<USER>/.cache/codacy/runtimes/python/bin/python3: no such file or directory binary=/home/<USER>/.cache/codacy/runtimes/python/bin/python3 path=/home/<USER>/.cache/codacy/runtimes/python/home/<USER>/.cache/codacy/runtimes/python/bin/python3
2025-06-11T21:20:08+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime version=3.11.11 error=failed to download and extract runtime python: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/python/bin/python3: chmod /home/<USER>/.cache/codacy/runtimes/python/home/<USER>/.cache/codacy/runtimes/python/bin/python3: no such file or directory runtime=python
2025-06-11T21:20:08+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=eslint version=8.57.0
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:100) Installing runtime-based tool tool=eslint version=8.57.0 runtime=node
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:159) Installing tool version=8.57.0 packageManager=npm packageManagerBin=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/npm command=install --prefix /home/<USER>/.cache/codacy/tools/eslint@8.57.0 eslint@8.57.0 @microsoft/eslint-formatter-sarif tool=eslint
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:189) Setting PATH environment for ESLint installation newPath=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools nodeDir=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin currentPath=/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:198) Running command command=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/npm install --prefix /home/<USER>/.cache/codacy/tools/eslint@8.57.0 eslint@8.57.0 @microsoft/eslint-formatter-sarif
2025-06-11T21:20:21+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:206) Tool installation completed tool=eslint version=8.57.0
2025-06-11T21:20:21+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=eslint version=8.57.0
2025-06-11T21:20:21+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=lizard version=1.17.19
2025-06-11T21:20:21+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:91) Installing Python tool version=1.17.19 tool=lizard
2025-06-11T21:20:21+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:290) Starting Python tool installation version=1.17.19 tool=lizard
2025-06-11T21:20:21+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:306) Creating Python virtual environment tool=lizard version=1.17.19 venvDir=/home/<USER>/.cache/codacy/tools/lizard@1.17.19/venv
2025-06-11T21:20:26+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:320) Installing Python package tool=lizard version=1.17.19 pipPath=/home/<USER>/.cache/codacy/tools/lizard@1.17.19/venv/bin/pip
2025-06-11T21:20:34+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:332) Python tool installation completed tool=lizard version=1.17.19
2025-06-11T21:20:34+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=lizard version=1.17.19
2025-06-11T21:20:34+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=pmd version=6.55.0
2025-06-11T21:20:34+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:80) Installing download-based tool version=6.55.0 downloadURL=https://github.com/pmd/pmd/releases/download/pmd_releases%2F6.55.0/pmd-bin-6.55.0.zip tool=pmd
2025-06-11T21:20:34+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:223) Downloading tool downloadPath=/home/<USER>/.cache/codacy/tools/pmd-bin-6.55.0.zip tool=pmd version=6.55.0 downloadURL=https://github.com/pmd/pmd/releases/download/pmd_releases%2F6.55.0/pmd-bin-6.55.0.zip
2025-06-11T21:20:37+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:257) Extracting tool tool=pmd version=6.55.0 fileName=pmd-bin-6.55.0.zip extractDirectory=/home/<USER>/.cache/codacy/tools/pmd@6.55.0
2025-06-11T21:20:38+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:282) Tool extraction completed tool=pmd version=6.55.0
2025-06-11T21:20:38+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=pmd version=6.55.0
2025-06-11T21:20:38+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=pylint version=3.3.6
2025-06-11T21:20:38+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:91) Installing Python tool version=3.3.6 tool=pylint
2025-06-11T21:20:38+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:290) Starting Python tool installation tool=pylint version=3.3.6
2025-06-11T21:20:38+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:306) Creating Python virtual environment tool=pylint version=3.3.6 venvDir=/home/<USER>/.cache/codacy/tools/pylint@3.3.6/venv
2025-06-11T21:20:44+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:320) Installing Python package tool=pylint version=3.3.6 pipPath=/home/<USER>/.cache/codacy/tools/pylint@3.3.6/venv/bin/pip
2025-06-11T21:20:52+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:332) Python tool installation completed tool=pylint version=3.3.6
2025-06-11T21:20:52+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=pylint version=3.3.6
2025-06-11T21:20:52+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=revive version=1.7.0
2025-06-11T21:20:52+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:100) Installing runtime-based tool version=1.7.0 runtime=go tool=revive
2025-06-11T21:20:52+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:159) Installing tool version=1.7.0 packageManager=go packageManagerBin=/home/<USER>/.cache/codacy/runtimes/go/bin/go command=install github.com/mgechev/revive@v1.7.0 tool=revive
2025-06-11T21:20:52+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:198) Running command command=/home/<USER>/.cache/codacy/runtimes/go/bin/go install github.com/mgechev/revive@v1.7.0
2025-06-11T21:21:19+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:206) Tool installation completed tool=revive version=1.7.0
2025-06-11T21:21:19+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=revive version=1.7.0
2025-06-11T21:21:19+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=semgrep version=1.78.0
2025-06-11T21:21:19+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:91) Installing Python tool tool=semgrep version=1.78.0
2025-06-11T21:21:19+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:290) Starting Python tool installation tool=semgrep version=1.78.0
2025-06-11T21:21:19+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:306) Creating Python virtual environment tool=semgrep version=1.78.0 venvDir=/home/<USER>/.cache/codacy/tools/semgrep@1.78.0/venv
2025-06-11T21:21:24+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:320) Installing Python package tool=semgrep version=1.78.0 pipPath=/home/<USER>/.cache/codacy/tools/semgrep@1.78.0/venv/bin/pip
2025-06-11T21:22:01+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:332) Python tool installation completed version=1.78.0 tool=semgrep
2025-06-11T21:22:01+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool version=1.78.0 tool=semgrep
2025-06-11T21:22:01+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=trivy version=0.59.1
2025-06-11T21:22:01+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:80) Installing download-based tool tool=trivy version=0.59.1 downloadURL=https://github.com/aquasecurity/trivy/releases/download/v0.59.1/trivy_0.59.1_Linux-64bit.tar.gz
2025-06-11T21:22:01+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:223) Downloading tool version=0.59.1 downloadURL=https://github.com/aquasecurity/trivy/releases/download/v0.59.1/trivy_0.59.1_Linux-64bit.tar.gz downloadPath=/home/<USER>/.cache/codacy/tools/trivy_0.59.1_Linux-64bit.tar.gz tool=trivy
2025-06-11T21:22:05+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:257) Extracting tool fileName=trivy_0.59.1_Linux-64bit.tar.gz extractDirectory=/home/<USER>/.cache/codacy/tools/trivy@0.59.1 tool=trivy version=0.59.1
2025-06-11T21:22:07+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:282) Tool extraction completed version=0.59.1 tool=trivy
2025-06-11T21:22:07+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool version=0.59.1 tool=trivy
2025-06-11T21:22:07+12:00 [INFO] (codacy-cli-v2/cmd/install.go:240) Installation completed successfully
2025-06-11T21:22:07+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:22:31+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:22:31+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:22:31+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif bootloader.asm] args=[bootloader.asm]
2025-06-11T21:22:41+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif bootloader.asm
2025-06-11T21:22:59+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:22:59+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:22:59+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[bootloader_minimal.asm] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif bootloader_minimal.asm]
2025-06-11T21:22:59+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif bootloader_minimal.asm
2025-06-11T21:23:06+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:23:06+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:23:06+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[Makefile] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif Makefile]
2025-06-11T21:23:06+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:84) Setting up Java environment javaHome=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:23:06+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:130) Updated environment variables path=PATH=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools binDir=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin javaBinary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java javaHome=JAVA_HOME=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:23:06+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:146) Running PMD command command=/home/<USER>/.cache/codacy/tools/pmd@6.55.0/pmd-bin-6.55.0/bin/run.sh pmd -R .codacy/tools-configs/ruleset.xml -d Makefile -f sarif -r /tmp/codacy-analysis-3770421990/pmd.sarif
2025-06-11T21:23:09+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif Makefile
2025-06-11T21:24:23+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:24:23+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:24:23+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif ../../../.config/Code/User/workspaceStorage/3f947ef6c7b0eed16dbdb6f7a8867b5f/Augment.vscode-augment/Augment-Memories] args=[../../../.config/Code/User/workspaceStorage/3f947ef6c7b0eed16dbdb6f7a8867b5f/Augment.vscode-augment/Augment-Memories]
2025-06-11T21:24:23+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif ../../../.config/Code/User/workspaceStorage/3f947ef6c7b0eed16dbdb6f7a8867b5f/Augment.vscode-augment/Augment-Memories
2025-06-11T21:24:36+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:84) Setting up Java environment javaHome=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:24:36+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:130) Updated environment variables javaBinary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java javaHome=JAVA_HOME=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7 path=PATH=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools binDir=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin
2025-06-11T21:24:36+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:146) Running PMD command command=/home/<USER>/.cache/codacy/tools/pmd@6.55.0/pmd-bin-6.55.0/bin/run.sh pmd -R .codacy/tools-configs/ruleset.xml -d ../../../.config/Code/User/workspaceStorage/3f947ef6c7b0eed16dbdb6f7a8867b5f/Augment.vscode-augment/Augment-Memories -f sarif -r /tmp/codacy-analysis-1905094682/pmd.sarif
2025-06-11T21:24:39+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:24:39+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:24:39+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_bootloader.asm] args=[uefi_bootloader.asm]
2025-06-11T21:24:39+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_bootloader.asm
2025-06-11T21:25:51+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:25:51+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:25:51+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_neural_core.asm] args=[uefi_neural_core.asm]
2025-06-11T21:25:51+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_neural_core.asm
2025-06-11T21:27:07+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:27:07+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:27:07+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[uefi_system_hooks.asm] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_system_hooks.asm]
2025-06-11T21:27:07+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_system_hooks.asm
2025-06-11T21:28:23+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:28:23+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:28:23+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_service_hooks.asm] args=[uefi_service_hooks.asm] command=analyze
2025-06-11T21:28:23+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_service_hooks.asm
2025-06-11T21:28:43+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:28:43+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:28:43+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif Makefile] args=[Makefile]
2025-06-11T21:28:44+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:84) Setting up Java environment javaHome=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:28:44+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:130) Updated environment variables binDir=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin javaBinary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java javaHome=JAVA_HOME=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7 path=PATH=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools
2025-06-11T21:28:44+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:146) Running PMD command command=/home/<USER>/.cache/codacy/tools/pmd@6.55.0/pmd-bin-6.55.0/bin/run.sh pmd -R .codacy/tools-configs/ruleset.xml -d Makefile -f sarif -r /tmp/codacy-analysis-3110673433/pmd.sarif
2025-06-11T21:28:46+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif Makefile
2025-06-11T21:29:20+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:29:20+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:29:20+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif Makefile] args=[Makefile]
2025-06-11T21:29:35+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:84) Setting up Java environment javaHome=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:29:35+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:130) Updated environment variables javaHome=JAVA_HOME=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7 path=PATH=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools binDir=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin javaBinary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java
2025-06-11T21:29:35+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:146) Running PMD command command=/home/<USER>/.cache/codacy/tools/pmd@6.55.0/pmd-bin-6.55.0/bin/run.sh pmd -R .codacy/tools-configs/ruleset.xml -d Makefile -f sarif -r /tmp/codacy-analysis-3085195267/pmd.sarif
2025-06-11T21:29:38+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif Makefile
2025-06-11T21:29:42+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:29:42+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:29:42+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_linker.ld] args=[uefi_linker.ld]
2025-06-11T21:29:42+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_linker.ld
2025-06-11T21:30:01+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:30:02+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:30:02+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif test_build.sh] args=[test_build.sh]
2025-06-11T21:30:02+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif test_build.sh
2025-06-11T21:30:32+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:30:32+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:30:32+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif ../../../.config/Code/User/settings.json] args=[../../../.config/Code/User/settings.json]
2025-06-11T21:30:32+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif ../../../.config/Code/User/settings.json
2025-06-11T21:30:57+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:30:57+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:30:57+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_bootloader_simple.asm] args=[uefi_bootloader_simple.asm]
2025-06-11T21:30:57+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_bootloader_simple.asm
2025-06-11T21:31:04+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:31:04+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:31:04+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif Makefile] args=[Makefile]
2025-06-11T21:31:04+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif Makefile
2025-06-11T21:31:17+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:84) Setting up Java environment javaHome=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:31:17+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:130) Updated environment variables path=PATH=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools binDir=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin javaBinary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java javaHome=JAVA_HOME=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:31:17+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:146) Running PMD command command=/home/<USER>/.cache/codacy/tools/pmd@6.55.0/pmd-bin-6.55.0/bin/run.sh pmd -R .codacy/tools-configs/ruleset.xml -d Makefile -f sarif -r /tmp/codacy-analysis-2611317281/pmd.sarif
2025-06-11T21:31:21+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:31:21+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:31:21+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif Makefile] args=[Makefile] command=analyze
2025-06-11T21:31:22+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif Makefile
2025-06-11T21:31:37+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:84) Setting up Java environment javaHome=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:31:37+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:130) Updated environment variables binDir=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin javaBinary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java javaHome=JAVA_HOME=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7 path=PATH=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools
2025-06-11T21:31:37+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:146) Running PMD command command=/home/<USER>/.cache/codacy/tools/pmd@6.55.0/pmd-bin-6.55.0/bin/run.sh pmd -R .codacy/tools-configs/ruleset.xml -d Makefile -f sarif -r /tmp/codacy-analysis-1824094636/pmd.sarif
2025-06-11T21:32:49+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:32:49+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:32:49+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[../../../.config/Code/User/settings.json] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif ../../../.config/Code/User/settings.json]
2025-06-11T21:32:49+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif ../../../.config/Code/User/settings.json
2025-06-11T21:34:48+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:34:48+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:34:48+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[TEST_RESULTS.md] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif TEST_RESULTS.md]
2025-06-11T21:34:48+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif TEST_RESULTS.md
2025-06-11T21:41:15+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:41:15+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:41:15+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_neural_core.asm] args=[uefi_neural_core.asm]
2025-06-11T21:41:15+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_neural_core.asm
2025-06-11T21:41:18+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:41:18+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:41:18+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif Makefile] args=[Makefile] command=analyze
2025-06-11T21:41:18+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:84) Setting up Java environment javaHome=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:41:18+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:130) Updated environment variables binDir=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin javaBinary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java javaHome=JAVA_HOME=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7 path=PATH=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools
2025-06-11T21:41:18+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:146) Running PMD command command=/home/<USER>/.cache/codacy/tools/pmd@6.55.0/pmd-bin-6.55.0/bin/run.sh pmd -R .codacy/tools-configs/ruleset.xml -d Makefile -f sarif -r /tmp/codacy-analysis-2559102000/pmd.sarif
2025-06-11T21:41:20+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif Makefile
2025-06-11T21:41:57+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:41:57+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:41:57+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif TEST_RESULTS.md] args=[TEST_RESULTS.md]
2025-06-11T21:41:57+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif TEST_RESULTS.md
2025-06-11T21:42:28+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:42:28+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:42:28+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif TEST_RESULTS.md] args=[TEST_RESULTS.md] command=analyze
2025-06-11T21:42:28+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif TEST_RESULTS.md
2025-06-11T21:48:14+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:48:14+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:48:14+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif TEST_RESULTS.md] args=[TEST_RESULTS.md]
2025-06-11T21:48:14+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif TEST_RESULTS.md
2025-06-11T21:49:23+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:49:23+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:49:23+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif syscall_hook.asm] args=[syscall_hook.asm]
2025-06-11T21:49:23+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif syscall_hook.asm
2025-06-11T21:49:28+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:49:28+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:49:28+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_neural_core.asm] args=[uefi_neural_core.asm] command=analyze
2025-06-11T21:49:28+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_neural_core.asm
2025-06-11T21:49:31+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:49:31+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:49:31+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif Makefile] args=[Makefile]
2025-06-11T21:49:32+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:84) Setting up Java environment javaHome=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7
2025-06-11T21:49:32+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:130) Updated environment variables javaHome=JAVA_HOME=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7 path=PATH=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools binDir=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin javaBinary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java
2025-06-11T21:49:32+12:00 [DEBUG] (codacy-cli-v2/tools/pmdRunner.go:146) Running PMD command command=/home/<USER>/.cache/codacy/tools/pmd@6.55.0/pmd-bin-6.55.0/bin/run.sh pmd -R .codacy/tools-configs/ruleset.xml -d Makefile -f sarif -r /tmp/codacy-analysis-2054858061/pmd.sarif
2025-06-11T21:49:34+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif Makefile
2025-06-11T21:50:40+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:50:40+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:50:40+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif rag_system.asm] args=[rag_system.asm]
2025-06-11T21:50:40+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif rag_system.asm
2025-06-11T21:50:42+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:50:42+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:50:42+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[syscall_hook.asm] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif syscall_hook.asm]
2025-06-11T21:50:42+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif syscall_hook.asm
2025-06-11T21:51:15+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:51:15+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:51:15+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif syscall_hook.asm] args=[syscall_hook.asm]
2025-06-11T21:51:15+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif syscall_hook.asm
2025-06-11T21:51:45+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:51:45+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:51:45+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif linker.ld] args=[linker.ld] command=analyze
2025-06-11T21:51:45+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif linker.ld
2025-06-11T21:52:13+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:52:13+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:52:13+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[README.md] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif README.md]
2025-06-11T21:52:13+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif README.md
2025-06-11T21:52:15+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:52:15+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:52:15+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif linker.ld] args=[linker.ld] command=analyze
2025-06-11T21:52:15+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif linker.ld
2025-06-11T21:52:53+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:52:53+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:52:53+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[uefi_service_hooks.asm] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif uefi_service_hooks.asm]
2025-06-11T21:52:53+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif uefi_service_hooks.asm
2025-06-11T21:55:33+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:55:33+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:55:33+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif neural_core.asm] args=[neural_core.asm] command=analyze
2025-06-11T21:55:33+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif neural_core.asm
2025-06-11T21:55:54+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:55:55+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:55:55+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif TEST_RESULTS.md] args=[TEST_RESULTS.md] command=analyze
2025-06-11T21:55:55+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif TEST_RESULTS.md
2025-06-11T21:56:55+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:56:55+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:56:55+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif neural_core.asm] args=[neural_core.asm] command=analyze
2025-06-11T21:56:55+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif neural_core.asm
