2025-06-11T21:18:57+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:18:57+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=init full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 init] args=[]
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 install] args=[] command=install
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:78) Starting installation process
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation runtime=python version=3.11.11
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation version=3.7.2 runtime=dart
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation runtime=go version=1.22.3
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation runtime=java version=17.0.10
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:107) Runtime scheduled for installation runtime=node version=22.2.0
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation tool=pylint version=3.3.6
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation tool=revive version=1.7.0
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation version=1.78.0 tool=semgrep
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation version=0.59.1 tool=trivy
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation tool=dartanalyzer version=3.7.2
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation version=8.57.0 tool=eslint
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation tool=lizard version=1.17.19
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:116) Tool scheduled for installation version=6.55.0 tool=pmd
2025-06-11T21:19:05+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=dart version=3.7.2
2025-06-11T21:19:05+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime version=3.7.2 downloadURL=https://storage.googleapis.com/dart-archive/channels/stable/release/3.7.2/sdk/dartsdk-linux-x64-release.zip downloadPath=/home/<USER>/.cache/codacy/runtimes/dartsdk-linux-x64-release.zip runtime=dart
2025-06-11T21:19:07+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:07+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:07+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command args=[test_build.sh] command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif test_build.sh]
2025-06-11T21:19:07+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif test_build.sh
2025-06-11T21:19:23+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime runtime=dart version=3.7.2 fileName=dartsdk-linux-x64-release.zip extractDirectory=/home/<USER>/.cache/codacy/runtimes
2025-06-11T21:19:28+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions binary=/home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart path=/home/<USER>/.cache/codacy/runtimes/dart-sdk/home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart error=chmod /home/<USER>/.cache/codacy/runtimes/dart-sdk/home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart: no such file or directory
2025-06-11T21:19:28+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime version=3.7.2 error=failed to download and extract runtime dart: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart: chmod /home/<USER>/.cache/codacy/runtimes/dart-sdk/home/<USER>/.cache/codacy/runtimes/dart-sdk/bin/dart: no such file or directory runtime=dart
2025-06-11T21:19:28+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=go version=1.22.3
2025-06-11T21:19:28+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime downloadURL=https://go.dev/dl/go1.22.3.linux-amd64.tar.gz downloadPath=/home/<USER>/.cache/codacy/runtimes/go1.22.3.linux-amd64.tar.gz runtime=go version=1.22.3
2025-06-11T21:19:36+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime fileName=go1.22.3.linux-amd64.tar.gz extractDirectory=/home/<USER>/.cache/codacy/runtimes runtime=go version=1.22.3
2025-06-11T21:19:38+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions binary=/home/<USER>/.cache/codacy/runtimes/go/bin/go path=/home/<USER>/.cache/codacy/runtimes/go/home/<USER>/.cache/codacy/runtimes/go/bin/go error=chmod /home/<USER>/.cache/codacy/runtimes/go/home/<USER>/.cache/codacy/runtimes/go/bin/go: no such file or directory
2025-06-11T21:19:38+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime runtime=go version=1.22.3 error=failed to download and extract runtime go: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/go/bin/go: chmod /home/<USER>/.cache/codacy/runtimes/go/home/<USER>/.cache/codacy/runtimes/go/bin/go: no such file or directory
2025-06-11T21:19:38+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=java version=17.0.10
2025-06-11T21:19:38+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime downloadPath=/home/<USER>/.cache/codacy/runtimes/OpenJDK17U-jdk_x64_linux_hotspot_17.0.10_7.tar.gz runtime=java version=17.0.10 downloadURL=https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.10%2B7/OpenJDK17U-jdk_x64_linux_hotspot_17.0.10_7.tar.gz
2025-06-11T21:19:55+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime runtime=java version=17.0.10 fileName=OpenJDK17U-jdk_x64_linux_hotspot_17.0.10_7.tar.gz extractDirectory=/home/<USER>/.cache/codacy/runtimes
2025-06-11T21:19:58+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:58+12:00 [INFO] (codacy-cli-v2/cli-v2.go:27) Starting Codacy CLI version=1.0.0-main.336.sha.73c0a88 (73c0a88) built at 2025-06-10T10:06:30Z
2025-06-11T21:19:58+12:00 [INFO] (codacy-cli-v2/cmd/root.go:34) Executing CLI command command=analyze full_command=[/home/<USER>/.cache/codacy/codacy-cli-v2/1.0.0-main.336.sha.73c0a88/codacy-cli-v2 analyze --format sarif PROJECT_STATUS.md] args=[PROJECT_STATUS.md]
2025-06-11T21:19:58+12:00 [DEBUG] (codacy-cli-v2/tools/revive/reviveRunner.go:53) Running Revive command command=/home/<USER>/.cache/codacy/tools/revive@1.7.0/revive -config .codacy/tools-configs/revive.toml -formatter sarif PROJECT_STATUS.md
2025-06-11T21:19:58+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions path=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java error=chmod /home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java: no such file or directory binary=/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java
2025-06-11T21:19:58+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime error=failed to download and extract runtime java: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java: chmod /home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/home/<USER>/.cache/codacy/runtimes/jdk-17.0.10+7/bin/java: no such file or directory runtime=java version=17.0.10
2025-06-11T21:19:58+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=node version=22.2.0
2025-06-11T21:19:58+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime runtime=node version=22.2.0 downloadURL=https://nodejs.org/dist/v22.2.0/node-v22.2.0-linux-x64.tar.gz downloadPath=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64.tar.gz
2025-06-11T21:20:03+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime version=22.2.0 fileName=node-v22.2.0-linux-x64.tar.gz extractDirectory=/home/<USER>/.cache/codacy/runtimes runtime=node
2025-06-11T21:20:04+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions binary=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node path=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node error=chmod /home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node: no such file or directory
2025-06-11T21:20:04+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime error=failed to download and extract runtime node: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node: chmod /home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/node: no such file or directory runtime=node version=22.2.0
2025-06-11T21:20:04+12:00 [INFO] (codacy-cli-v2/cmd/install.go:157) Installing runtime runtime=python version=3.11.11
2025-06-11T21:20:04+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:88) Downloading runtime downloadPath=/home/<USER>/.cache/codacy/runtimes/cpython-3.11.11+20250317-x86_64-unknown-linux-gnu-install_only.tar.gz runtime=python version=3.11.11 downloadURL=https://github.com/astral-sh/python-build-standalone/releases/download/20250317/cpython-3.11.11+20250317-x86_64-unknown-linux-gnu-install_only.tar.gz
2025-06-11T21:20:07+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:116) Extracting runtime version=3.11.11 fileName=cpython-3.11.11+20250317-x86_64-unknown-linux-gnu-install_only.tar.gz extractDirectory=/home/<USER>/.cache/codacy/runtimes runtime=python
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/runtimes-installer.go:137) Failed to set binary permissions error=chmod /home/<USER>/.cache/codacy/runtimes/python/home/<USER>/.cache/codacy/runtimes/python/bin/python3: no such file or directory binary=/home/<USER>/.cache/codacy/runtimes/python/bin/python3 path=/home/<USER>/.cache/codacy/runtimes/python/home/<USER>/.cache/codacy/runtimes/python/bin/python3
2025-06-11T21:20:08+12:00 [ERROR] (codacy-cli-v2/cmd/install.go:163) Failed to install runtime version=3.11.11 error=failed to download and extract runtime python: failed to set binary permissions for /home/<USER>/.cache/codacy/runtimes/python/bin/python3: chmod /home/<USER>/.cache/codacy/runtimes/python/home/<USER>/.cache/codacy/runtimes/python/bin/python3: no such file or directory runtime=python
2025-06-11T21:20:08+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=eslint version=8.57.0
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:100) Installing runtime-based tool tool=eslint version=8.57.0 runtime=node
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:159) Installing tool version=8.57.0 packageManager=npm packageManagerBin=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/npm command=install --prefix /home/<USER>/.cache/codacy/tools/eslint@8.57.0 eslint@8.57.0 @microsoft/eslint-formatter-sarif tool=eslint
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:189) Setting PATH environment for ESLint installation newPath=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin:/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools nodeDir=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin currentPath=/home/<USER>/.linuxbrew/bin:/home/<USER>/.linuxbrew/sbin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/sbin:/sbin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/snap/bin:/home/<USER>/.dotnet/tools
2025-06-11T21:20:08+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:198) Running command command=/home/<USER>/.cache/codacy/runtimes/node-v22.2.0-linux-x64/bin/npm install --prefix /home/<USER>/.cache/codacy/tools/eslint@8.57.0 eslint@8.57.0 @microsoft/eslint-formatter-sarif
2025-06-11T21:20:21+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:206) Tool installation completed tool=eslint version=8.57.0
2025-06-11T21:20:21+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=eslint version=8.57.0
2025-06-11T21:20:21+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=lizard version=1.17.19
2025-06-11T21:20:21+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:91) Installing Python tool version=1.17.19 tool=lizard
2025-06-11T21:20:21+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:290) Starting Python tool installation version=1.17.19 tool=lizard
2025-06-11T21:20:21+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:306) Creating Python virtual environment tool=lizard version=1.17.19 venvDir=/home/<USER>/.cache/codacy/tools/lizard@1.17.19/venv
2025-06-11T21:20:26+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:320) Installing Python package tool=lizard version=1.17.19 pipPath=/home/<USER>/.cache/codacy/tools/lizard@1.17.19/venv/bin/pip
2025-06-11T21:20:34+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:332) Python tool installation completed tool=lizard version=1.17.19
2025-06-11T21:20:34+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=lizard version=1.17.19
2025-06-11T21:20:34+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=pmd version=6.55.0
2025-06-11T21:20:34+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:80) Installing download-based tool version=6.55.0 downloadURL=https://github.com/pmd/pmd/releases/download/pmd_releases%2F6.55.0/pmd-bin-6.55.0.zip tool=pmd
2025-06-11T21:20:34+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:223) Downloading tool downloadPath=/home/<USER>/.cache/codacy/tools/pmd-bin-6.55.0.zip tool=pmd version=6.55.0 downloadURL=https://github.com/pmd/pmd/releases/download/pmd_releases%2F6.55.0/pmd-bin-6.55.0.zip
2025-06-11T21:20:37+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:257) Extracting tool tool=pmd version=6.55.0 fileName=pmd-bin-6.55.0.zip extractDirectory=/home/<USER>/.cache/codacy/tools/pmd@6.55.0
2025-06-11T21:20:38+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:282) Tool extraction completed tool=pmd version=6.55.0
2025-06-11T21:20:38+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=pmd version=6.55.0
2025-06-11T21:20:38+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=pylint version=3.3.6
2025-06-11T21:20:38+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:91) Installing Python tool version=3.3.6 tool=pylint
2025-06-11T21:20:38+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:290) Starting Python tool installation tool=pylint version=3.3.6
2025-06-11T21:20:38+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:306) Creating Python virtual environment tool=pylint version=3.3.6 venvDir=/home/<USER>/.cache/codacy/tools/pylint@3.3.6/venv
2025-06-11T21:20:44+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:320) Installing Python package tool=pylint version=3.3.6 pipPath=/home/<USER>/.cache/codacy/tools/pylint@3.3.6/venv/bin/pip
2025-06-11T21:20:52+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:332) Python tool installation completed tool=pylint version=3.3.6
2025-06-11T21:20:52+12:00 [INFO] (codacy-cli-v2/cmd/install.go:201) Successfully installed tool tool=pylint version=3.3.6
2025-06-11T21:20:52+12:00 [INFO] (codacy-cli-v2/cmd/install.go:185) Installing tool tool=revive version=1.7.0
2025-06-11T21:20:52+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:100) Installing runtime-based tool version=1.7.0 runtime=go tool=revive
2025-06-11T21:20:52+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:159) Installing tool version=1.7.0 packageManager=go packageManagerBin=/home/<USER>/.cache/codacy/runtimes/go/bin/go command=install github.com/mgechev/revive@v1.7.0 tool=revive
2025-06-11T21:20:52+12:00 [DEBUG] (codacy-cli-v2/config/tools-installer.go:198) Running command command=/home/<USER>/.cache/codacy/runtimes/go/bin/go install github.com/mgechev/revive@v1.7.0
